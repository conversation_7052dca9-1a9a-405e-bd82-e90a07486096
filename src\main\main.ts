/**
 * @file src/main/main.ts
 * @description Main process entry point for the Electron application.
 * Handles window creation, application lifecycle events, and IPC.
 */

import { app, BrowserWindow, ipcMain, dialog, Menu, session } from 'electron';
import path from 'path';
import { basename } from 'path';
import fs from 'fs/promises';
import { Worker } from 'worker_threads';
import { mcpApiService } from './services/mcp_api';
import { isMac } from './utils/environment';
import { getTokenCount } from './services/langchain-splitter';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow: BrowserWindow | null = null;
let indexerWorker: Worker | null = null;
let langChainWorker: Worker | null = null;

// Проверяем, запущен ли dev сервер
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;
const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'] || (isDev ? 'http://localhost:5173' : null);

console.log('[Main] Environment check:');
console.log('[Main] NODE_ENV:', process.env.NODE_ENV);
console.log('[Main] isPackaged:', app.isPackaged);
console.log('[Main] isDev:', isDev);
console.log('[Main] VITE_DEV_SERVER_URL:', VITE_DEV_SERVER_URL);

const createWindow = async () => {
  // Set up Content Security Policy for security
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          isDev
            ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: http://localhost:*; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:*; style-src 'self' 'unsafe-inline' http://localhost:*;"
            : "default-src 'self' data: blob:; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:;"
        ]
      }
    });
  });

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true, // Explicitly enable web security
    },
  });

  if (VITE_DEV_SERVER_URL) {
    console.log('[Main] Attempting to load Vite dev server:', VITE_DEV_SERVER_URL);
    await loadDevServer();
    mainWindow.webContents.openDevTools();
  } else {
    console.log('[Main] Loading production build...');
    mainWindow.loadFile(path.join(__dirname, '../index.html'));
    mainWindow.webContents.openDevTools();
  }
};

const loadDevServer = async (retries = 2, delay = 1500) => {
  console.log(`[Main] Starting dev server connection attempts to: ${VITE_DEV_SERVER_URL}`);
  
  for (let i = 0; i < retries; i++) {
    try {
      console.log(`[Main] Attempting to load Vite dev server (attempt ${i + 1}/${retries})...`);
      
      // Загружаем URL напрямую без fetch проверки
      await mainWindow!.loadURL(VITE_DEV_SERVER_URL!);
      console.log('[Main] ✅ Successfully loaded Vite dev server');
      return;
    } catch (error) {
      console.log(`[Main] ❌ Failed to load Vite dev server (attempt ${i + 1}/${retries}):`, error);
      if (i < retries - 1) {
        console.log(`[Main] ⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('[Main] ❌ Failed to load Vite dev server after all retries.');
  console.log('[Main] 🔄 Attempting to load fallback (production build)...');
  
  // Fallback to production build if available
  try {
    const fallbackPath = path.join(__dirname, '../index.html');
    console.log('[Main] Fallback path:', fallbackPath);
    await mainWindow!.loadFile(fallbackPath);
    console.log('[Main] ✅ Fallback loaded successfully');
  } catch (fallbackError) {
    console.error('[Main] ❌ Failed to load fallback file:', fallbackError);
    // Последний fallback - простой HTML
    mainWindow!.loadURL('data:text/html;charset=utf-8,<html><body style="background:#1e1e1e;color:white;padding:20px;font-family:Arial;"><h1>❌ Ошибка загрузки</h1><p>Не удалось загрузить ни dev сервер, ни production файлы.</p><p>Проверьте консоль для деталей.</p></body></html>');
  }
};

const createIndexerWorker = () => {
  const workerPath = path.join(__dirname, 'indexer.worker.js'); // Legacy worker
  indexerWorker = new Worker(workerPath);
  indexerWorker.on('message', (result) => {
    console.log('[Main] Received from legacy worker:', result);
    mainWindow?.webContents.send('indexing-update', result);
  });
  indexerWorker.on('error', (error) => console.error('[Main] Legacy worker error:', error));
  indexerWorker.on('exit', (code) => {
    if (code !== 0) console.error(`[Main] Legacy worker stopped with exit code ${code}`);
  });
};

const createLangChainWorker = () => {
  const workerPath = path.join(__dirname, 'langchain-indexer.worker.js');
  langChainWorker = new Worker(workerPath);
  langChainWorker.on('message', (result) => {
    console.log('[Main] Received from LangChain worker:', result);
    mainWindow?.webContents.send('langchain-indexing-update', result);
  });
  langChainWorker.on('error', (error) => console.error('[Main] LangChain worker error:', error));
  langChainWorker.on('exit', (code) => {
    if (code !== 0) console.error(`[Main] LangChain worker stopped with exit code ${code}`);
    langChainWorker = null;
  });
};

function createApplicationMenu() {
  const template = [
    ...(isMac ? [{
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    {
      label: 'File',
      submenu: [
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template as any);
  Menu.setApplicationMenu(menu);
}

app.on('ready', async () => {
  // Set a Content Security Policy
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          isDev
            ? "default-src 'self' 'unsafe-inline' data:; script-src 'self' 'unsafe-eval' 'unsafe-inline' data:"
            : "default-src 'self'"
        ]
      }
    });
  });

  createApplicationMenu();
  await createWindow();
  createIndexerWorker(); // Legacy worker for backward compatibility
  createLangChainWorker(); // New LangChain worker
  mcpApiService.start();
});

app.on('window-all-closed', () => {
  mcpApiService.stop();
  indexerWorker?.terminate();
  // langChainWorker?.terminate();
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

// --- IPC Handlers ---

// Legacy IPC handlers removed - using only LangChain handlers now

ipcMain.handle('dialog:openDirectory', async () => {
  console.log('[Main] Opening directory dialog...');
  try {
    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow!, {
      properties: ['openDirectory']
    });
    console.log('[Main] Dialog result:', { canceled, filePaths });
    if (canceled) return null;
    return filePaths[0];
  } catch (error) {
    console.error('[Main] Error opening directory dialog:', error);
    return null;
  }
});

// --- LangChain RAG IPC Handlers ---

// LangChain RAG - индексация выбранных файлов
ipcMain.handle('langchain:indexSelectedFiles', async (event, { filePaths, projectId, config }: {
  filePaths: string[];
  projectId: number;
  config?: any;
}) => {
  console.log('[Main] Posting batch indexing job to LangChain worker for project', projectId);

  if (!langChainWorker) {
    createLangChainWorker();
  }

  try {
    langChainWorker?.postMessage({
      type: 'index_batch',
      filePaths,
      projectId,
      config
    });

    return {
      success: true,
      message: `Started batch indexing ${filePaths.length} files with LangChain`
    };

  } catch (error) {
    console.error('[Main] Error in LangChain indexSelectedFiles:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

// LangChain RAG - индексация одного файла
ipcMain.handle('langchain:indexFile', async (event, { filePath, projectId, config }: {
  filePath: string;
  projectId: number;
  config?: any;
}) => {
  console.log('[Main] Starting LangChain indexing of file:', filePath, 'for project', projectId);

  if (!langChainWorker) {
    createLangChainWorker();
  }

  try {
    langChainWorker?.postMessage({
      type: 'index_file',
      filePath,
      projectId,
      config
    });

    return {
      success: true,
      message: `Started indexing ${filePath} with LangChain`
    };

  } catch (error) {
    console.error('[Main] Error in LangChain indexFile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

// LangChain RAG - переиндексация файла
ipcMain.handle('langchain:reindexFile', async (event, { filePath, projectId, config }: {
  filePath: string;
  projectId: number;
  config?: any;
}) => {
  console.log('[Main] Starting LangChain reindexing of file:', filePath, 'for project', projectId);

  if (!langChainWorker) {
    createLangChainWorker();
  }

  try {
    langChainWorker?.postMessage({
      type: 'reindex_file',
      filePath,
      projectId,
      config
    });

    return {
      success: true,
      message: `Started reindexing ${filePath} with LangChain`
    };

  } catch (error) {
    console.error('[Main] Error in LangChain reindexFile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

// LangChain RAG - выполнение запроса
ipcMain.handle('langchain:query', async (event, { projectId, question, options }: {
  projectId: number;
  question: string;
  options?: any;
}) => {
  console.log('[Main] LangChain RAG query for project', projectId, ':', question);

  try {
    // Import RAG service directly for queries (not in worker to avoid serialization issues)
    const { ragService } = await import('./services/langchain-rag');

    const response = await ragService.query(projectId, question, options);

    return {
      success: true,
      response
    };

  } catch (error) {
    console.error('[Main] Error in LangChain query:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

// LangChain RAG - получение статистики проекта
ipcMain.handle('langchain:getProjectStats', async (event, { projectId }: { projectId: number }) => {
  console.log('[Main] Getting LangChain project stats for project', projectId);

  try {
    const { ragService } = await import('./services/langchain-rag');

    const stats = await ragService.getProjectStats(projectId);

    return {
      success: true,
      stats
    };

  } catch (error) {
    console.error('[Main] Error getting LangChain project stats:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

// LangChain RAG - обновление конфигурации
ipcMain.handle('langchain:updateConfig', async (event, { config }: { config: any }) => {
  console.log('[Main] Updating LangChain config:', config);

  if (!langChainWorker) {
    createLangChainWorker();
  }

  try {
    langChainWorker?.postMessage({
      type: 'update_config',
      config
    });

    return {
      success: true,
      message: 'LangChain config updated'
    };

  } catch (error) {
    console.error('[Main] Error updating LangChain config:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
});

// LangChain RAG - отмена индексации
ipcMain.handle('langchain:cancelIndexing', async () => {
  console.log('[Main] Cancelling LangChain indexing...');
  if (langChainWorker) {
    langChainWorker.postMessage({ type: 'cancel_indexing' });
    return { success: true, message: 'Indexing cancellation requested.' };
  }
  return { success: false, message: 'Worker not running.' };
});

// LangChain RAG - очистка chunks проекта
ipcMain.handle('langchain:clearProjectChunks', async (event, { projectId }: { projectId: number }) => {
  console.log('[Main] Clearing chunks for project', projectId);

  try {
    const { ragService } = await import('./services/langchain-rag');
    await ragService.clearProjectChunks(projectId);

    return {
      success: true,
      message: `Chunks cleared for project ${projectId}`
    };
  } catch (error) {
    console.error('[Main] Error clearing project chunks:', error);
    return {
      success: false,
      message: `Error clearing chunks: ${(error as Error).message}`
    };
  }
});

// --- Migration IPC Handlers removed - no longer needed ---

// --- Project IPC Handlers ---
import { sqliteService } from './services/sqlite';

ipcMain.handle('projects:getAll', async () => {
  return sqliteService.getAllProjects();
});

ipcMain.handle('projects:add', async (event, projectPath: string) => {
  console.log('[Main] Adding project:', projectPath);
  try {
    const name = basename(projectPath);
    console.log('[Main] Project name:', name);

    // Check for duplicates
    if (sqliteService.getProjectByName(name)) {
      throw new Error(`Project with name "${name}" already exists.`);
    }

    sqliteService.addProject(name, projectPath);
    const newProject = sqliteService.getProjectByName(name);
    console.log('[Main] Project added successfully:', newProject);
    return newProject;
  } catch (error: any) {
    console.error('[Main] Failed to add project:', error);
    dialog.showErrorBox('Error Adding Project', error.message);
    return null;
  }
});

ipcMain.handle('projects:delete', async (event, id: number) => {
  sqliteService.deleteProject(id);
});

// --- Project Files IPC Handlers ---
import { projectFilesService } from './services/project-files';

ipcMain.handle('projectFiles:addSelected', async (event, payload: { filePaths: string[]; projectId: number; copyFiles?: boolean }) => {
  try {
    return await projectFilesService.addSelectedFiles(payload);
  } catch (error) {
    console.error('[Main] Error adding selected files:', error);
    throw error;
  }
});

ipcMain.handle('projectFiles:getSelected', async (event, projectId: number) => {
  try {
    return await projectFilesService.getSelectedFiles(projectId);
  } catch (error) {
    console.error('[Main] Error getting selected files:', error);
    return [];
  }
});

ipcMain.handle('projectFiles:removeSelected', async (event, projectId: number, fileId: string) => {
  try {
    await projectFilesService.removeSelectedFile(projectId, fileId);
  } catch (error) {
    console.error('[Main] Error removing selected file:', error);
    throw error;
  }
});

ipcMain.handle('projectFiles:clearSelected', async (event, projectId: number) => {
  try {
    await projectFilesService.clearSelectedFiles(projectId);
  } catch (error) {
    console.error('[Main] Error clearing selected files:', error);
    throw error;
  }
});

// Recursive function to read directory structure with file sizes
async function readDirectory(dirPath: string): Promise<any[]> {
  const dirents = await fs.readdir(dirPath, { withFileTypes: true });
  const files = await Promise.all(dirents.map(async (dirent) => {
    const res = path.resolve(dirPath, dirent.name);
    if (dirent.isDirectory()) {
      const children = await readDirectory(res);
      // Calculate total size of directory
      const totalSize = children.reduce((sum, child) => sum + (child.size || 0), 0);
      return {
        id: res,
        name: dirent.name,
        children,
        size: totalSize,
        isDirectory: true
      };
    } else {
      try {
        const stats = await fs.stat(res);
        return {
          id: res,
          name: dirent.name,
          size: stats.size,
          isDirectory: false
        };
      } catch (error) {
        // If we can't read the file, return 0 size
        return {
          id: res,
          name: dirent.name,
          size: 0,
          isDirectory: false
        };
      }
    }
  }));

  // Sort directories first, then files, all alphabetically
  files.sort((a, b) => {
    if (a.isDirectory && !b.isDirectory) return -1;
    if (!a.isDirectory && b.isDirectory) return 1;
    return a.name.localeCompare(b.name);
  });

  return files;
}

ipcMain.handle('fs:readDirectory', async (event, dirPath: string) => {
  try {
    return await readDirectory(dirPath);
  } catch (error) {
    console.error(`Failed to read directory ${dirPath}:`, error);
    return null;
  }
});

// New lazy loading function with file sizes
async function readDirectoryLazy(dirPath: string): Promise<any[]> {
  const dirents = await fs.readdir(dirPath, { withFileTypes: true });
  const files = await Promise.all(dirents.map(async (dirent) => {
    const res = path.resolve(dirPath, dirent.name);
    const isDirectory = dirent.isDirectory();
    let hasChildren = false;
    let size = 0;

    if (isDirectory) {
      try {
        const subDirents = await fs.readdir(res, { withFileTypes: true });
        hasChildren = subDirents.length > 0;
      } catch (e) {
        // Ignore errors for sub-directories (e.g. permissions)
      }
    } else {
      try {
        const stats = await fs.stat(res);
        size = stats.size;
      } catch (e) {
        // If we can't read the file, size remains 0
      }
    }

    return {
      id: res,
      name: dirent.name,
      children: isDirectory ? [] : undefined, // Important for lazy loading
      hasChildren: isDirectory && hasChildren,
      size,
      isDirectory
    };
  }));

  // Sort directories first, then files, all alphabetically
  files.sort((a, b) => {
    if (a.hasChildren && !b.hasChildren) return -1;
    if (!a.hasChildren && b.hasChildren) return 1;
    return a.name.localeCompare(b.name);
  });

  return files;
}

ipcMain.handle('fs:readDirectoryLazy', async (event, dirPath: string) => {
  try {
    return await readDirectoryLazy(dirPath);
  } catch (error) {
    console.error(`Failed to read directory lazily ${dirPath}:`, error);
    return null;
  }
});

ipcMain.handle('fs:readFile', async (event, filePath: string) => {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    console.error(`Failed to read file ${filePath}:`, error);
    throw error;
  }
});

ipcMain.handle('fs:getTokenCount', async (event, filePath: string) => {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return getTokenCount(content);
  } catch (error) {
    // For binary files or files that can't be read, return 0
    return 0;
  }
});

// App Data Management
ipcMain.handle('app:clearData', async () => {
  try {
    const { app } = require('electron');
    const fs = require('fs').promises;
    const path = require('path');

    // Получаем пути к данным приложения
    const userDataPath = app.getPath('userData');
    const appDataPath = app.getPath('appData');

    console.log('[Main] Clearing app data...');
    console.log('[Main] UserData path:', userDataPath);

    // Очищаем базу данных SQLite
    sqliteService.clearAllData();

    // Очищаем кеш и временные файлы
    const cachePath = path.join(userDataPath, 'cache');
    const tempPath = path.join(userDataPath, 'temp');

    try {
      await fs.rmdir(cachePath, { recursive: true });
      console.log('[Main] Cleared cache directory');
    } catch (error) {
      console.log('[Main] Cache directory not found or already cleared');
    }

    try {
      await fs.rmdir(tempPath, { recursive: true });
      console.log('[Main] Cleared temp directory');
    } catch (error) {
      console.log('[Main] Temp directory not found or already cleared');
    }

    console.log('[Main] App data cleared successfully');
  } catch (error) {
    console.error('[Main] Error clearing app data:', error);
    throw error;
  }
});
