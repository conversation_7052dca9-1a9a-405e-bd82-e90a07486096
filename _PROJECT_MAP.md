# Metacharts Project Map (High-Level Index)
# Auto-generated: 2025-07-30T15:12:07.576Z
# Purpose: Provides a high-level overview for AI navigation and developer onboarding.
# Stats: 38 files, ~9k lines, ~287k chars, ~~72k tokens

> Legend: An ellipsis (…) at the end of a description means it was truncated. Read the file for full details.

## `./`
- `package.json`: No description found.
- `tsconfig.json`: No description found.

## `src/main/`
- `main.ts`: src/main/main.ts

## `src/main/services/`
- `indexer.worker.ts`: src/main/services/indexer.worker.ts
- `lancedb.ts`: src/main/services/lancedb.ts
- `langchain-indexer.worker.ts`: No description found.
- `langchain-llm.ts`: No description found.
- `langchain-rag.ts`: No description found.
- `langchain-splitter.ts`: No description found.
- `langchain-vectorstore.ts`: No description found.
- `llm.ts`: src/main/services/llm.ts
- `mcp_api.ts`: src/main/services/mcp_api.ts
- `project-files.ts`: src/main/services/project-files.ts
- `simple-chunker.ts`: No description found.
- `sqlite.ts`: src/main/services/sqlite.ts

## `src/main/services/legacy/`
- `simple-chunker.ts`: No description found.

## `src/main/utils/`
- `environment.ts`: No description found.
- `tokenizer.ts`: No description found.

## `src/renderer/`
- `App.tsx`: src/renderer/App.tsx
- `main.tsx`: src/renderer/main.tsx
- `preload.ts`: src/renderer/preload.ts

## `src/renderer/components/`
- `ChatPanel.tsx`: src/renderer/components/ChatPanel.tsx
- `DashboardPanel.tsx`: src/renderer/components/DashboardPanel.tsx
- `EmptyTabContent.tsx`: src/renderer/components/EmptyTabContent.tsx
- `ProjectsPanel.tsx`: src/renderer/components/ProjectsPanel.tsx
- `ProjectTabBar.tsx`: src/renderer/components/ProjectTabBar.tsx
- `QueryInterface.tsx`: src/renderer/components/QueryInterface.tsx
- `SettingsPanel.tsx`: src/renderer/components/SettingsPanel.tsx
- `Sidebar.tsx`: src/renderer/components/Sidebar.tsx

## `src/renderer/components/ExplorerPanel/`
- `ChunkingPanel.tsx`: No description found.
- `FileExplorerPanel.tsx`: src/renderer/components/FileExplorerPanel.tsx
- `FileTree.tsx`: No description found.
- `FilterSettingsDialog.tsx`: src/renderer/components/ExplorerPanel/FilterSettingsDialog.tsx
- `ProjectExplorer.tsx`: src/renderer/components/ProjectExplorer.tsx

## `src/shared/`
- `store.ts`: store.ts

## `src/shared/types/`
- `filters.types.ts`: src/shared/types/filters.types.ts

## `src/shared/utils/`
- `fileFilters.ts`: src/shared/utils/fileFilters.ts
- `formatters.ts`: formatters.ts