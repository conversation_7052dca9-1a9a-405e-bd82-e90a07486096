/**
 * @file src/renderer/components/ExplorerPanel/FilterSettingsDialog.tsx
 * @description Dialog for configuring auto-select filters
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  ExpandMore,
  RestoreOutlined,
  InfoOutlined
} from '@mui/icons-material';
import { useAppStore } from '../../../shared/store';
import type { AutoSelectFilters } from '../../../shared/types/filters.types';
import { DEFAULT_AUTO_SELECT_FILTERS, FILTER_PRESETS } from '../../../shared/types/filters.types';

interface FilterSettingsDialogProps {
  open: boolean;
  onClose: () => void;
}

export const FilterSettingsDialog: React.FC<FilterSettingsDialogProps> = ({ open, onClose }) => {
  const { autoSelectFilters, setAutoSelectFilters } = useAppStore();
  const [localFilters, setLocalFilters] = useState<AutoSelectFilters>(autoSelectFilters);
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['fileTypes']);

  // Sync with store when dialog opens
  useEffect(() => {
    if (open) {
      setLocalFilters(autoSelectFilters);
    }
  }, [open, autoSelectFilters]);

  const handlePanelChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedPanels(prev => 
      isExpanded 
        ? [...prev, panel]
        : prev.filter(p => p !== panel)
    );
  };

  const handleSave = () => {
    setAutoSelectFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    setLocalFilters(DEFAULT_AUTO_SELECT_FILTERS);
  };

  const handlePresetApply = (presetId: string) => {
    const preset = FILTER_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setLocalFilters(preset.filters);
    }
  };

  const updateFileTypeFilter = (category: keyof AutoSelectFilters['fileTypes'], enabled: boolean) => {
    setLocalFilters(prev => ({
      ...prev,
      fileTypes: {
        ...prev.fileTypes,
        [category]: {
          ...prev.fileTypes[category],
          enabled
        }
      }
    }));
  };

  const updateDirectoryFilter = (category: keyof AutoSelectFilters['directories'], enabled: boolean) => {
    setLocalFilters(prev => ({
      ...prev,
      directories: {
        ...prev.directories,
        [category]: {
          ...prev.directories[category],
          enabled
        }
      }
    }));
  };

  const updateHiddenFiles = (enabled: boolean) => {
    setLocalFilters(prev => ({
      ...prev,
      hiddenFiles: { enabled }
    }));
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: '#1a1a1a',
          border: '1px solid rgba(168, 85, 247, 0.3)',
          borderRadius: 2
        }
      }}
    >
      <DialogTitle sx={{ 
        color: '#a855f7', 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1,
        borderBottom: '1px solid rgba(168, 85, 247, 0.2)',
        pb: 2
      }}>
        <InfoOutlined />
        Auto-Select Filter Settings
        <Tooltip title="Reset to defaults">
          <IconButton 
            onClick={handleReset}
            sx={{ ml: 'auto', color: '#64748b' }}
          >
            <RestoreOutlined />
          </IconButton>
        </Tooltip>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {/* Quick Presets */}
        <Box sx={{ p: 2, borderBottom: '1px solid rgba(168, 85, 247, 0.1)' }}>
          <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
            Quick Presets
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {FILTER_PRESETS.map(preset => (
              <Chip
                key={preset.id}
                label={preset.name}
                onClick={() => handlePresetApply(preset.id)}
                sx={{
                  backgroundColor: 'rgba(168, 85, 247, 0.1)',
                  color: '#c084fc',
                  border: '1px solid rgba(168, 85, 247, 0.3)',
                  '&:hover': {
                    backgroundColor: 'rgba(168, 85, 247, 0.2)'
                  }
                }}
              />
            ))}
          </Box>
        </Box>

        {/* File Types */}
        <Accordion 
          expanded={expandedPanels.includes('fileTypes')}
          onChange={handlePanelChange('fileTypes')}
          sx={{ 
            backgroundColor: 'transparent',
            '&:before': { display: 'none' }
          }}
        >
          <AccordionSummary expandIcon={<ExpandMore sx={{ color: '#a855f7' }} />}>
            <Typography sx={{ color: '#d8b4fe', fontWeight: 600 }}>
              File Types
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.fileTypes.images.enabled}
                    onChange={(e) => updateFileTypeFilter('images', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Images
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      {localFilters.fileTypes.images.extensions.join(', ')}
                    </Typography>
                  </Box>
                }
              />
              
              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.fileTypes.vectors.enabled}
                    onChange={(e) => updateFileTypeFilter('vectors', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Vector Graphics
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      {localFilters.fileTypes.vectors.extensions.join(', ')}
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.fileTypes.icons.enabled}
                    onChange={(e) => updateFileTypeFilter('icons', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Icons & Icon Directories
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      **/icons/**, favicon.*, etc.
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.fileTypes.logs.enabled}
                    onChange={(e) => updateFileTypeFilter('logs', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Log Files
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      {localFilters.fileTypes.logs.extensions.join(', ')}
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.fileTypes.lockFiles.enabled}
                    onChange={(e) => updateFileTypeFilter('lockFiles', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Lock Files
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      package-lock.json, yarn.lock, etc.
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.fileTypes.binary.enabled}
                    onChange={(e) => updateFileTypeFilter('binary', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Binary Files
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      {localFilters.fileTypes.binary.extensions.join(', ')}
                    </Typography>
                  </Box>
                }
              />
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Directories */}
        <Accordion 
          expanded={expandedPanels.includes('directories')}
          onChange={handlePanelChange('directories')}
          sx={{ 
            backgroundColor: 'transparent',
            '&:before': { display: 'none' }
          }}
        >
          <AccordionSummary expandIcon={<ExpandMore sx={{ color: '#a855f7' }} />}>
            <Typography sx={{ color: '#d8b4fe', fontWeight: 600 }}>
              Directories
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.directories.nodeModules.enabled}
                    onChange={(e) => updateDirectoryFilter('nodeModules', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Node Modules
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      node_modules directories
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.directories.buildOutputs.enabled}
                    onChange={(e) => updateDirectoryFilter('buildOutputs', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Build Outputs
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      dist, build, out, target, bin, obj
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.directories.versionControl.enabled}
                    onChange={(e) => updateDirectoryFilter('versionControl', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude Version Control
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      .git, .svn, .hg directories
                    </Typography>
                  </Box>
                }
              />

              <FormControlLabel
                control={
                  <Switch 
                    checked={localFilters.directories.ideFiles.enabled}
                    onChange={(e) => updateDirectoryFilter('ideFiles', e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                      Exclude IDE Files
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      .vscode, .idea, __pycache__
                    </Typography>
                  </Box>
                }
              />
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Other Settings */}
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 2 }}>
            Other Settings
          </Typography>
          
          <FormControlLabel
            control={
              <Switch 
                checked={localFilters.hiddenFiles.enabled}
                onChange={(e) => updateHiddenFiles(e.target.checked)}
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Exclude Hidden Files
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b' }}>
                  Files starting with dot (.)
                </Typography>
              </Box>
            }
          />
        </Box>

        <Alert 
          severity="info" 
          sx={{ 
            m: 2, 
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            '& .MuiAlert-icon': { color: '#3b82f6' }
          }}
        >
          These filters only affect the Auto Select button. Manual selection is always available.
        </Alert>
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: '1px solid rgba(168, 85, 247, 0.2)' }}>
        <Button 
          onClick={onClose}
          sx={{ color: '#64748b' }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: '#a855f7',
            '&:hover': { backgroundColor: '#9333ea' }
          }}
        >
          Save Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
};
