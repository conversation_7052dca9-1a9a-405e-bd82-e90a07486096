/**
 * @file src/shared/utils/fileFilters.ts
 * @description Centralized file filtering utilities for auto-select functionality
 */

import type { AutoSelectFilters } from '../types/filters.types';

/**
 * Parse gitignore patterns from file content
 */
export const parseGitignorePatterns = (content: string): string[] => {
  return content
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.startsWith('#')) // Remove empty lines and comments
    .filter(line => !line.startsWith('!')); // Remove negation patterns for simplicity
};

/**
 * Check if a file/directory matches gitignore patterns
 * Simplified but correct gitignore implementation for auto-select
 */
const matchesGitignorePattern = (name: string, fullPath: string, pattern: string): boolean => {
  // Skip empty patterns
  if (!pattern.trim()) return false;

  const originalPattern = pattern.trim();

  // Handle directory patterns (ending with /)
  if (originalPattern.endsWith('/')) {
    // Directory patterns only match directories, not files
    // For auto-select, we assume isLeaf=false means directory
    const dirPattern = originalPattern.slice(0, -1);
    return matchesPattern(name, dirPattern) || matchesPattern(fullPath, dirPattern);
  }

  // Handle absolute paths (starting with /)
  if (originalPattern.startsWith('/')) {
    const pathPattern = originalPattern.slice(1);
    return matchesPattern(fullPath, pathPattern);
  }

  // For other patterns, match against filename only
  return matchesPattern(name, originalPattern);
};

/**
 * Helper function to match a string against a pattern with wildcards
 */
const matchesPattern = (str: string, pattern: string): boolean => {
  // Convert gitignore pattern to regex
  // Escape special regex characters except * and ?
  let regexPattern = pattern
    .replace(/[.+^${}()|[\]\\]/g, '\\$&') // Escape special chars
    .replace(/\*/g, '.*')  // * matches any sequence of characters
    .replace(/\?/g, '.');  // ? matches any single character

  const regex = new RegExp(`^${regexPattern}$`, 'i');
  return regex.test(str);
};

/**
 * Check if a file matches file type filters
 */
const matchesFileTypeFilters = (name: string, fullPath: string, filters: AutoSelectFilters): boolean => {
  const lowerName = name.toLowerCase();
  const { fileTypes } = filters;

  // Images - exact extension match
  if (fileTypes.images.enabled &&
      fileTypes.images.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
    return true;
  }

  // Vector graphics - exact extension match
  if (fileTypes.vectors.enabled &&
      fileTypes.vectors.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
    return true;
  }

  // Log files - extension and pattern match
  if (fileTypes.logs.enabled) {
    if (fileTypes.logs.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
      return true;
    }
    if (fileTypes.logs.patterns?.some(pattern => {
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(name);
      }
      return false;
    })) {
      return true;
    }
  }

  // Icon files - extension and path-based patterns
  if (fileTypes.icons.enabled) {
    // Check if it's an icon file by extension
    if (fileTypes.icons.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
      return true;
    }

    // Check path-based patterns for icon directories
    if (fileTypes.icons.patterns?.some(pattern => {
      if (pattern.includes('**/')) {
        // Path-based pattern like **/icons/** - check if any path segment matches exactly
        const pathSegment = pattern.replace(/\*\*/g, '').replace(/\*/g, '').replace(/\//g, '');
        const pathSegments = fullPath.toLowerCase().split('/');
        return pathSegments.some(segment => segment === pathSegment.toLowerCase());
      } else if (pattern.includes('*')) {
        // File pattern like favicon.*
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(name);
      }
      return false;
    })) {
      return true;
    }

    // Only check for exact favicon files, not any file containing "icon"
    if (lowerName.startsWith('favicon.')) {
      return true;
    }
  }

  // Binary files - exact extension match
  if (fileTypes.binary.enabled &&
      fileTypes.binary.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
    return true;
  }

  // Lock files - extension and exact pattern match
  if (fileTypes.lockFiles.enabled) {
    if (fileTypes.lockFiles.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
      return true;
    }
    if (fileTypes.lockFiles.patterns?.some(pattern => {
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(name);
      }
      return lowerName === pattern.toLowerCase();
    })) {
      return true;
    }
  }

  return false;
};

/**
 * Check if a directory matches directory filters
 */
const matchesDirectoryFilters = (name: string, filters: AutoSelectFilters): boolean => {
  const lowerName = name.toLowerCase();
  const { directories } = filters;

  // Node modules - exact name match only
  if (directories.nodeModules.enabled) {
    if (directories.nodeModules.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  // Build outputs - exact name match only
  if (directories.buildOutputs.enabled) {
    if (directories.buildOutputs.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  // Version control - exact name match only
  if (directories.versionControl.enabled) {
    if (directories.versionControl.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  // IDE files - exact name match only
  if (directories.ideFiles.enabled) {
    if (directories.ideFiles.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  return false;
};

/**
 * Main function to determine if a file/directory should be ignored in auto-select
 */
export const shouldIgnoreFile = (
  name: string,
  isLeaf: boolean,
  fullPath: string,
  gitignorePatterns: string[] = [],
  filters?: AutoSelectFilters
): boolean => {
  // Always check gitignore patterns first
  for (const pattern of gitignorePatterns) {
    if (matchesGitignorePattern(name, fullPath, pattern)) {
      return true;
    }
  }

  // If no filters provided, don't filter anything else (let user choose)
  if (!filters) {
    return false;
  }

  const { hiddenFiles } = filters;

  // Check hidden files (both files and directories)
  if (hiddenFiles.enabled && name.startsWith('.')) {
    return true;
  }

  // For files - check file type filters
  if (isLeaf) {
    return matchesFileTypeFilters(name, fullPath, filters);
  } else {
    // For directories - check directory filters with exact matching
    return matchesDirectoryFilters(name, filters);
  }
};

/**
 * Read .gitignore file from project root
 */
export const readGitignoreFile = async (projectPath: string): Promise<string[]> => {
  try {
    const gitignorePath = `${projectPath}/.gitignore`;
    const content = await window.electronAPI.readFile(gitignorePath);
    return parseGitignorePatterns(content);
  } catch (error) {
    // .gitignore file doesn't exist or can't be read
    return [];
  }
};
