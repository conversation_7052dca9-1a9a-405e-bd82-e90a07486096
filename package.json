{"name": "smart-rag", "productName": "SmartRAG", "version": "1.0.0", "description": "RAG Code Assistant with MCP support", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "test": "jest", "test:watch": "jest --watch", "gg": "ts-node _gg.ts"}, "author": "AETHERIUM SWARM", "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@lancedb/lancedb": "^0.12.0", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66", "@langchain/openai": "^0.6.3", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-virtual": "^3.13.12", "antd": "^5.26.6", "better-sqlite3": "^11.10.0", "date-fns": "^4.1.0", "electron-squirrel-startup": "^1.0.1", "express": "^5.1.0", "js-tiktoken": "^1.0.20", "langchain": "^0.3.30", "react": "^19.1.0", "react-arborist": "^3.4.3", "react-dom": "^19.1.0", "tiktoken": "^1.0.21", "use-debounce": "^10.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@electron/rebuild": "^4.0.1", "@types/better-sqlite3": "^7.6.13", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "chalk": "^5.4.1", "concurrently": "^9.2.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "glob": "^11.0.3", "jest": "^30.0.5", "npm-check-updates": "^18.0.2", "tailwindcss": "^4.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "wait-on": "^8.0.4"}}