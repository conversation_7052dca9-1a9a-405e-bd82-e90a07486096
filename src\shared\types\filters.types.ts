/**
 * @file src/shared/types/filters.types.ts
 * @description Type definitions for auto-select filters configuration
 */

/**
 * Configuration for file type filtering in auto-select
 */
export interface FileTypeFilter {
  /** Whether this filter category is enabled */
  enabled: boolean;
  /** File extensions to filter (with or without leading dot) */
  extensions: string[];
  /** Additional patterns to match */
  patterns?: string[];
}

/**
 * Configuration for directory filtering in auto-select
 */
export interface DirectoryFilter {
  /** Whether to exclude this directory type */
  enabled: boolean;
  /** Directory names or patterns to match */
  patterns: string[];
}

/**
 * Complete auto-select filters configuration
 */
export interface AutoSelectFilters {
  /** File type filters organized by category */
  fileTypes: {
    /** Image files (png, jpg, jpeg, gif, webp, ico) */
    images: FileTypeFilter;
    /** Vector graphics and icons (svg) */
    vectors: FileTypeFilter;
    /** Log files and temporary files */
    logs: FileTypeFilter;
    /** Icon directories and files */
    icons: FileTypeFilter;
    /** Database and binary files */
    binary: FileTypeFilter;
    /** Lock files (package-lock.json, yarn.lock, etc.) */
    lockFiles: FileTypeFilter;
  };
  
  /** Directory filters */
  directories: {
    /** Node.js dependencies */
    nodeModules: DirectoryFilter;
    /** Build output directories */
    buildOutputs: DirectoryFilter;
    /** Version control directories */
    versionControl: DirectoryFilter;
    /** IDE and editor directories */
    ideFiles: DirectoryFilter;
  };
  
  /** Hidden files (starting with dot) */
  hiddenFiles: {
    enabled: boolean;
  };

  /** Gitignore integration */
  gitignore: {
    enabled: boolean;
    autoLoad: boolean; // Automatically load .gitignore from project root
  };

  /** Custom gitignore patterns */
  customPatterns: {
    enabled: boolean;
    patterns: string[];
  };
}

/**
 * Default auto-select filters configuration
 */
export const DEFAULT_AUTO_SELECT_FILTERS: AutoSelectFilters = {
  fileTypes: {
    images: {
      enabled: true,
      extensions: ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.tiff'],
      patterns: []
    },
    vectors: {
      enabled: false, // Don't exclude SVG by default - many are used for icons but some for graphics
      extensions: ['.svg'],
      patterns: []
    },
    logs: {
      enabled: true,
      extensions: ['.log', '.tmp', '.temp'],
      patterns: ['*.log.*', '*.tmp.*']
    },
    icons: {
      enabled: true,
      extensions: ['.ico'], // Only ICO files, not SVG/PNG which might be used for other purposes
      patterns: ['**/icons/**', '**/icon/**', 'favicon.*']
    },
    binary: {
      enabled: true,
      extensions: ['.db', '.sqlite', '.sqlite3', '.gpg', '.bin', '.exe', '.dll'],
      patterns: []
    },
    lockFiles: {
      enabled: true,
      extensions: ['.lock'],
      patterns: ['*-lock.*', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml']
    }
  },

  directories: {
    nodeModules: {
      enabled: true,
      patterns: ['node_modules'] // Only exact match, not nested patterns
    },
    buildOutputs: {
      enabled: true,
      patterns: ['dist', 'build', 'out', 'target', 'bin', 'obj', '.next', '.nuxt']
    },
    versionControl: {
      enabled: true,
      patterns: ['.git', '.svn', '.hg', '.bzr']
    },
    ideFiles: {
      enabled: true,
      patterns: ['.vscode', '.idea', '.vs', '__pycache__', '.pytest_cache']
    }
  },

  hiddenFiles: {
    enabled: false // Don't exclude hidden files by default - some are important config files
  },

  gitignore: {
    enabled: true, // Enable gitignore by default
    autoLoad: true // Automatically load .gitignore from project root
  },

  customPatterns: {
    enabled: true,
    patterns: []
  }
};

/**
 * Filter preset configurations for quick setup
 */
export interface FilterPreset {
  id: string;
  name: string;
  description: string;
  filters: AutoSelectFilters;
}

export const FILTER_PRESETS: FilterPreset[] = [
  {
    id: 'strict',
    name: 'Strict Filtering',
    description: 'Excludes all non-code files, images, logs, and build artifacts',
    filters: DEFAULT_AUTO_SELECT_FILTERS
  },
  {
    id: 'moderate',
    name: 'Moderate Filtering',
    description: 'Excludes images and logs but includes some config files',
    filters: {
      ...DEFAULT_AUTO_SELECT_FILTERS,
      fileTypes: {
        ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes,
        vectors: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.vectors, enabled: false },
        lockFiles: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.lockFiles, enabled: false }
      }
    }
  },
  {
    id: 'minimal',
    name: 'Minimal Filtering',
    description: 'Only excludes obvious non-code files like node_modules and build outputs',
    filters: {
      ...DEFAULT_AUTO_SELECT_FILTERS,
      fileTypes: {
        images: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.images, enabled: false },
        vectors: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.vectors, enabled: false },
        logs: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.logs, enabled: false },
        icons: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.icons, enabled: false },
        binary: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.binary, enabled: true },
        lockFiles: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.lockFiles, enabled: false }
      },
      hiddenFiles: { enabled: false },
      gitignore: { ...DEFAULT_AUTO_SELECT_FILTERS.gitignore, enabled: true }
    }
  }
];
