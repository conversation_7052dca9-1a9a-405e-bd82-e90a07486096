/**
 * @file src/shared/hooks/useGitignore.ts
 * @description Hook for managing gitignore patterns
 */

import { useEffect } from 'react';
import { useAppStore } from '../store';
import { readGitignoreFile } from '../utils/fileFilters';

export const useGitignore = (projectPath?: string) => {
  const { 
    autoSelectFilters, 
    gitignorePatterns, 
    setGitignorePatterns 
  } = useAppStore();

  // Load gitignore patterns when project or settings change
  useEffect(() => {
    const loadGitignorePatterns = async () => {
      if (
        projectPath && 
        autoSelectFilters.gitignore.enabled && 
        autoSelectFilters.gitignore.autoLoad
      ) {
        try {
          const patterns = await readGitignoreFile(projectPath);
          setGitignorePatterns(patterns);
        } catch (error) {
          console.error('Error loading gitignore patterns:', error);
          setGitignorePatterns([]);
        }
      } else {
        setGitignorePatterns([]);
      }
    };

    loadGitignorePatterns();
  }, [
    projectPath, 
    autoSelectFilters.gitignore.enabled, 
    autoSelectFilters.gitignore.autoLoad, 
    setGitignorePatterns
  ]);

  return {
    gitignorePatterns,
    isGitignoreEnabled: autoSelectFilters.gitignore.enabled,
    isAutoLoadEnabled: autoSelectFilters.gitignore.autoLoad,
    setGitignorePatterns
  };
};
