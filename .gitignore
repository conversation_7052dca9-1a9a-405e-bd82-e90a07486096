# ===================================================================
# SmartRAG Project .gitignore
# AI-powered RAG Code Assistant with MCP support
# Technologies: Electron, React, TypeScript, LangChain, SQLite, LanceDB
# ===================================================================

# === NODE.JS & NPM ===
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# === ELECTRON BUILD OUTPUTS ===
dist-electron/
out/
build/
release/

# === VITE & FRONTEND BUILD ===
dist/
.vite/
.rollup.cache/

# === TYPESCRIPT ===
*.tsbuildinfo
.tscache/

# === ENVIRONMENT & SECRETS ===
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.key
*.pem

# === DATABASE & STORAGE ===
database/
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm

# === AI/ML & RAG SPECIFIC ===
tiktoken_cache/
data/
embeddings/
vector_store/
.langchain/
.openai/
model_cache/

# === LOGS & DEBUGGING ===
logs/
*.log
debug.log
error.log
combined.log
lerna-debug.log*

# === TEMPORARY & CACHE FILES ===
temp/
.temp/
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# === OPERATING SYSTEM ===
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
Desktop.ini
$RECYCLE.BIN/

# Linux
# *~
.fuse_hidden*
.directory
.Trash-*

# === IDE & EDITORS ===
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Vim
# *.swp
# *.swo
# *~

# Emacs
# *~
# \#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# === TESTING ===
coverage/
.nyc_output/
.jest/
test-results/
playwright-report/
test-results.xml

# === PACKAGE MANAGERS ===
.yarn/
.pnp.*
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# === MISC ===
*.tgz
*.tar.gz
.eslintcache
.stylelintcache

# === PROJECT SPECIFIC ===
# Keep important config files
!package.json
!package-lock.json
!tsconfig.json
!tsconfig.node.json
!vite.config.ts
!tailwind.config.js
!.gitignore
!README.md

# Exclude generated documentation
docs/build/
docs/.docusaurus/

# === SECURITY ===
# Never commit these
*.p12
*.pfx
*.key
*.crt
*.csr
*.pem
auth.json
credentials.json
service-account.json
