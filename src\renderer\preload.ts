/**
 * @file src/renderer/preload.ts
 * @description Preload script for the renderer process.
 * Exposes Node.js APIs to the renderer process in a secure way using contextBridge.
 */

import { contextBridge, ipcRenderer } from 'electron';
import type { IpcApi, IndexingUpdatePayload } from '../shared/ipc.d';

const ipcApi: IpcApi = {
  // Indexing
  startIndexing: (payload: { filePath: string; projectId: number }) => {
    ipcRenderer.send('start-indexing', payload);
  },
  onIndexingUpdate: (callback: (payload: IndexingUpdatePayload) => void) => {
    const listener = (event: any, payload: IndexingUpdatePayload) => callback(payload);
    ipcRenderer.on('indexing-update', listener);
    return () => {
      ipcRenderer.removeListener('indexing-update', listener);
    };
  },

  // LangChain RAG - индексация выбранных файлов
  indexSelectedFiles: (payload: { filePaths: string[]; projectId: number; }) => {
    return ipcRenderer.invoke('langchain:indexSelectedFiles', payload);
  },

  // LangChain indexing updates
  onLangChainIndexingUpdate: (callback: (payload: any) => void) => {
    const listener = (_event: any, payload: any) => callback(payload);
    ipcRenderer.on('langchain-indexing-update', listener);
    return () => {
      ipcRenderer.removeListener('langchain-indexing-update', listener);
    };
  },

  // LangChain RAG - отмена индексации
  cancelIndexing: () => ipcRenderer.invoke('langchain:cancelIndexing'),

  // LangChain RAG - очистка chunks проекта
  clearProjectChunks: (projectId: number) => ipcRenderer.invoke('langchain:clearProjectChunks', { projectId }),

  // Projects
  getAllProjects: () => ipcRenderer.invoke('projects:getAll'),
  addProject: (path: string) => ipcRenderer.invoke('projects:add', path),
  deleteProject: (id: number) => ipcRenderer.invoke('projects:delete', id),

  // Project Files
  addSelectedFiles: (payload: { filePaths: string[]; projectId: number; copyFiles?: boolean }) =>
    ipcRenderer.invoke('projectFiles:addSelected', payload),
  getSelectedFiles: (projectId: number) => ipcRenderer.invoke('projectFiles:getSelected', projectId),
  removeSelectedFile: (projectId: number, fileId: string) =>
    ipcRenderer.invoke('projectFiles:removeSelected', projectId, fileId),
  clearSelectedFiles: (projectId: number) => ipcRenderer.invoke('projectFiles:clearSelected', projectId),

  // FS
  openDirectoryDialog: () => ipcRenderer.invoke('dialog:openDirectory'),
  readDirectory: (path: string) => ipcRenderer.invoke('fs:readDirectory', path),
  readDirectoryLazy: (path: string) => ipcRenderer.invoke('fs:readDirectoryLazy', path),
  readFile: (filePath: string) => ipcRenderer.invoke('fs:readFile', filePath),
  getTokenCount: (filePath: string) => ipcRenderer.invoke('fs:getTokenCount', filePath),

  // App Data
  clearAppData: () => ipcRenderer.invoke('app:clearData'),
};

contextBridge.exposeInMainWorld('electronAPI', ipcApi);
